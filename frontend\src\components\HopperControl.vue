<template>
  <div class="hopper-control-card bg-white rounded-lg shadow-md p-6 border border-gray-200">
    <!-- <PERSON> -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-800">
        Hopper {{ drumId }} - Scraping Blade
      </h3>
      <div class="flex items-center space-x-2">
        <div 
          :class="[
            'w-3 h-3 rounded-full',
            isBladeRunning ? 'bg-green-500' : 'bg-gray-400'
          ]"
          :title="isBladeRunning ? 'Running' : 'Stopped'"
        ></div>
        <span class="text-sm text-gray-600">
          {{ isBladeRunning ? 'Running' : 'Stopped' }}
        </span>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
      {{ errorMessage }}
    </div>

    <!-- <PERSON> Screws Status -->
    <div v-if="bladeScrews && bladeScrews.length > 0" class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-3">Blade Screws Status</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div 
          v-for="screw in bladeScrews" 
          :key="screw.id"
          class="bg-gray-50 p-3 rounded border"
        >
          <div class="flex justify-between items-center mb-2">
            <span class="font-medium text-sm">Screw {{ screw.id }}</span>
            <div 
              :class="[
                'w-2 h-2 rounded-full',
                screw.running ? 'bg-green-500' : 'bg-gray-400'
              ]"
            ></div>
          </div>
          <div class="text-xs text-gray-600">
            Position: {{ formatPosition(screw.position) }} µm
          </div>
        </div>
      </div>
    </div>

    <!-- Collective Blade Motion Control -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-3">Collective Blade Motion</h4>
      
      <!-- Motion Mode Selection -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">Motion Mode</label>
        <select 
          v-model="collectiveMotion.mode"
          :disabled="!connected"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
        >
          <option value="relative">Relative</option>
          <option value="absolute">Absolute</option>
          <option value="homing">Homing</option>
        </select>
      </div>

      <!-- Distance Input (for non-homing modes) -->
      <div v-if="collectiveMotion.mode !== 'homing'" class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">Distance (µm)</label>
        <input 
          v-model.number="collectiveMotion.distance"
          type="number"
          step="100"
          min="0"
          max="50000"
          :disabled="!connected"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
          placeholder="Enter distance in micrometers"
        />
      </div>

      <!-- Motion Buttons -->
      <div class="flex space-x-2">
        <button 
          @click="startCollectiveMotion"
          :disabled="!connected || isBladeRunning"
          class="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
        >
          Start Motion
        </button>
        <button 
          @click="cancelCollectiveMotion"
          :disabled="!connected || !isBladeRunning"
          class="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-gray-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
        >
          Cancel Motion
        </button>
      </div>
    </div>

    <!-- Individual Screw Control -->
    <div class="mb-4">
      <h4 class="text-md font-medium text-gray-700 mb-3">Individual Screw Control</h4>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div 
          v-for="screwId in [0, 1]" 
          :key="screwId"
          class="bg-gray-50 p-4 rounded border"
        >
          <h5 class="font-medium text-sm mb-3">Screw {{ screwId }}</h5>
          
          <!-- Distance Input -->
          <div class="mb-3">
            <label class="block text-xs font-medium text-gray-700 mb-1">Distance (µm)</label>
            <input 
              v-model.number="individualMotion[screwId].distance"
              type="number"
              step="100"
              min="-50000"
              max="50000"
              :disabled="!connected"
              class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100"
              placeholder="Relative distance"
            />
          </div>

          <!-- Control Buttons -->
          <div class="flex space-x-1">
            <button 
              @click="startIndividualMotion(screwId)"
              :disabled="!connected || getScrewRunning(screwId)"
              class="flex-1 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white px-2 py-1 rounded text-xs font-medium transition-colors"
            >
              Move
            </button>
            <button 
              @click="cancelIndividualMotion(screwId)"
              :disabled="!connected || !getScrewRunning(screwId)"
              class="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-gray-300 text-white px-2 py-1 rounded text-xs font-medium transition-colors"
            >
              Stop
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import apiService from '@/services/api'

export default {
  name: 'HopperControl',
  props: {
    drumId: {
      type: Number,
      required: true
    },
    bladeScrews: {
      type: Array,
      default: () => []
    },
    bladeMotion: {
      type: Object,
      default: () => ({})
    },
    connected: {
      type: Boolean,
      default: false
    }
  },
  emits: ['error', 'motion-started', 'motion-cancelled'],
  setup(props, { emit }) {
    // Motion parameters
    const collectiveMotion = ref({
      mode: 'relative',
      distance: 1000.0
    })

    const individualMotion = ref({
      0: { distance: 500.0 },
      1: { distance: 500.0 }
    })

    // Error handling
    const errorMessage = ref('')

    // Clear error after 5 seconds
    watch(errorMessage, (newError) => {
      if (newError) {
        setTimeout(() => {
          errorMessage.value = ''
        }, 5000)
      }
    })

    // Computed properties
    const isBladeRunning = computed(() => {
      return props.bladeScrews?.some(screw => screw.running) || false
    })

    const getScrewRunning = (screwId) => {
      const screw = props.bladeScrews?.find(s => s.id === screwId)
      return screw?.running || false
    }

    // Utility functions
    const formatPosition = (position) => {
      return typeof position === 'number' ? position.toFixed(1) : '0.0'
    }

    // Motion control functions
    const startCollectiveMotion = async () => {
      try {
        console.log(`Starting collective blade motion for drum ${props.drumId}`)
        
        const motionData = {
          mode: collectiveMotion.value.mode,
          distance: collectiveMotion.value.mode !== 'homing' ? collectiveMotion.value.distance : undefined
        }

        await apiService.setBladeScrewsMotion(props.drumId, motionData)
        emit('motion-started', { drumId: props.drumId, type: 'collective', motion: motionData })
        errorMessage.value = ''
      } catch (error) {
        console.error(`Error starting collective blade motion for drum ${props.drumId}:`, error)
        errorMessage.value = error.response?.data?.detail || 'Failed to start blade motion'
        emit('error', error)
      }
    }

    const cancelCollectiveMotion = async () => {
      try {
        console.log(`Cancelling collective blade motion for drum ${props.drumId}`)
        await apiService.cancelBladeScrewsMotion(props.drumId)
        emit('motion-cancelled', { drumId: props.drumId, type: 'collective' })
        errorMessage.value = ''
      } catch (error) {
        console.error(`Error cancelling collective blade motion for drum ${props.drumId}:`, error)
        errorMessage.value = error.response?.data?.detail || 'Failed to cancel blade motion'
        emit('error', error)
      }
    }

    const startIndividualMotion = async (screwId) => {
      try {
        console.log(`Starting individual blade motion for drum ${props.drumId}, screw ${screwId}`)
        
        const motionData = {
          distance: individualMotion.value[screwId].distance
        }

        await apiService.setBladeScrewMotion(props.drumId, screwId, motionData)
        emit('motion-started', { drumId: props.drumId, screwId, type: 'individual', motion: motionData })
        errorMessage.value = ''
      } catch (error) {
        console.error(`Error starting individual blade motion for drum ${props.drumId}, screw ${screwId}:`, error)
        errorMessage.value = error.response?.data?.detail || 'Failed to start screw motion'
        emit('error', error)
      }
    }

    const cancelIndividualMotion = async (screwId) => {
      try {
        console.log(`Cancelling individual blade motion for drum ${props.drumId}, screw ${screwId}`)
        await apiService.cancelBladeScrewMotion(props.drumId, screwId)
        emit('motion-cancelled', { drumId: props.drumId, screwId, type: 'individual' })
        errorMessage.value = ''
      } catch (error) {
        console.error(`Error cancelling individual blade motion for drum ${props.drumId}, screw ${screwId}:`, error)
        errorMessage.value = error.response?.data?.detail || 'Failed to cancel screw motion'
        emit('error', error)
      }
    }

    return {
      collectiveMotion,
      individualMotion,
      errorMessage,
      isBladeRunning,
      getScrewRunning,
      formatPosition,
      startCollectiveMotion,
      cancelCollectiveMotion,
      startIndividualMotion,
      cancelIndividualMotion
    }
  }
}
</script>

<style scoped>
.hopper-control-card {
  min-height: 400px;
}

.hopper-control-card h3 {
  color: #1f2937;
}

.hopper-control-card h4 {
  color: #374151;
}

.hopper-control-card h5 {
  color: #4b5563;
}
</style>
