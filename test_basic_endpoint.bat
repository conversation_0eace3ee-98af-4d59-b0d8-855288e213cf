@echo off
REM Test Recoater API Endpoints
REM This script tests the Recoater API and keeps the window open to view results

echo Testing Recoater API Endpoints
echo ==============================
echo.

echo [1] Testing connection to Recoater API...
curl -v http://*************:8080/
echo.
pause

echo [2] Testing /state endpoint...
curl -v -H "Accept: application/json" http://*************:8080/state
echo.
pause

echo [3] Testing /axis/x endpoint...
curl -v -H "Accept: application/json" http://*************:8080/axis/x
echo.
pause

echo [4] Testing /axis/z endpoint...
curl -v -H "Accept: application/json" http://*************:8080/axis/z
echo.

echo API Testing Complete!
echo.
pause
