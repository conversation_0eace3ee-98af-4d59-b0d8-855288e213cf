<template>
  <div class="print-view">
    <h2 class="view-title">Print Control</h2>
    
    <!-- Connection Status -->
    <div class="status-card">
      <div class="status-header">
        <h3 class="status-title">Connection Status</h3>
        <div class="status-indicator">
          <div 
            :class="[
              'status-dot',
              statusStore.isConnected ? 'status-connected' : 'status-disconnected'
            ]"
          ></div>
          <span class="status-text">
            {{ statusStore.isConnected ? 'Connected' : 'Disconnected' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Layer Parameters -->
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title">Layer Parameters</h3>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to configure layer parameters</p>
        </div>
        
        <div class="parameter-grid">
          <!-- Filling Drum ID -->
          <div class="parameter-group">
            <label for="filling-id" class="parameter-label">Filling Drum ID</label>
            <input
              id="filling-id"
              v-model.number="layerParams.filling_id"
              type="number"
              class="parameter-input"
              :disabled="!statusStore.isConnected || isLoading"
              min="-1"
              step="1"
            />
            <p class="parameter-help">Set to -1 for no filling material</p>
          </div>

          <!-- Patterning Speed -->
          <div class="parameter-group">
            <label for="speed" class="parameter-label">Patterning Speed (mm/s)</label>
            <input
              id="speed"
              v-model.number="layerParams.speed"
              type="number"
              class="parameter-input"
              :disabled="!statusStore.isConnected || isLoading"
              min="0.1"
              step="0.1"
            />
          </div>

          <!-- X Offset -->
          <div class="parameter-group">
            <label for="x-offset" class="parameter-label">X Offset (mm)</label>
            <input
              id="x-offset"
              v-model.number="layerParams.x_offset"
              type="number"
              class="parameter-input"
              :disabled="!statusStore.isConnected || isLoading"
              min="0"
              step="0.1"
            />
          </div>

          <!-- Powder Saving -->
          <div class="parameter-group">
            <label class="parameter-label">
              <input
                v-model="layerParams.powder_saving"
                type="checkbox"
                class="parameter-checkbox"
                :disabled="!statusStore.isConnected || isLoading"
              />
              Enable Powder Saving
            </label>
            <p class="parameter-help">Use powder saving strategies</p>
          </div>
        </div>

        <div class="button-group">
          <button
            @click="loadParameters"
            :disabled="!statusStore.isConnected || isLoading"
            class="btn btn-secondary"
          >
            <span v-if="isLoading">Loading...</span>
            <span v-else>Load Current</span>
          </button>
          <button
            @click="saveParameters"
            :disabled="!statusStore.isConnected || isLoading || !isParametersValid"
            class="btn btn-primary"
          >
            <span v-if="isLoading">Saving...</span>
            <span v-else>Save Parameters</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Layer Preview -->
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title">Layer Preview</h3>
        <div class="card-subtitle">
          Preview shows the current layer configuration based on the parameters above
        </div>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to view layer preview</p>
        </div>
        
        <div class="preview-container">
          <div v-if="previewLoading" class="preview-loading">
            <div class="loading-spinner"></div>
            <p>Loading preview...</p>
          </div>
          
          <div v-else-if="previewImageUrl" class="preview-image-container">
            <img 
              :src="previewImageUrl" 
              alt="Layer Preview" 
              class="preview-image"
              @error="handlePreviewError"
            />
          </div>
          
          <div v-else class="preview-placeholder">
            <svg class="preview-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <p>No preview available</p>
          </div>
        </div>

        <div class="preview-controls">
          <div class="form-group">
            <label for="preview-drum-select" class="form-label">Preview Source:</label>
            <select
              id="preview-drum-select"
              v-model="previewSource"
              class="form-select"
              :disabled="!statusStore.isConnected || previewLoading"
            >
              <option value="layer">Current Layer Configuration</option>
              <option v-for="drumId in availableDrums" :key="`drum-${drumId}`" :value="`drum-${drumId}`">
                Drum {{ drumId }} Geometry
              </option>
            </select>
          </div>

          <div class="button-group">
            <button
              @click="loadPreview"
              :disabled="!statusStore.isConnected || previewLoading"
              class="btn btn-primary"
            >
              <span v-if="previewLoading">Loading...</span>
              <span v-else>Load Preview</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- File Management -->
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title">File Management</h3>
        <div class="card-subtitle">
          Upload CLI/PNG files to drums for use during printing. Files are stored on individual drums.
        </div>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to manage files</p>
        </div>

        <div class="file-management-grid">
          <!-- File Upload Section -->
          <div class="file-upload-section">
            <h4 class="section-title">Upload Geometry File</h4>
            <div class="form-group">
              <label for="drum-select" class="form-label">Target Drum:</label>
              <select
                id="drum-select"
                v-model="selectedDrumId"
                class="form-select"
                :disabled="!statusStore.isConnected || isFileOperationLoading"
              >
                <option value="">Select a drum</option>
                <option v-for="drumId in availableDrums" :key="drumId" :value="drumId">
                  Drum {{ drumId }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label for="file-input" class="form-label">Geometry File (PNG or CLI):</label>
              <input
                id="file-input"
                type="file"
                ref="fileInput"
                @change="handleFileSelect"
                accept=".png,.cli,image/png,application/octet-stream"
                class="form-file-input"
                :disabled="!statusStore.isConnected || isFileOperationLoading"
              />
              <div v-if="selectedFile" class="file-info">
                Selected: {{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
              </div>
            </div>

            <div class="button-group">
              <button
                @click="uploadFile"
                :disabled="!statusStore.isConnected || !selectedFile || selectedDrumId === '' || isFileOperationLoading"
                class="btn btn-primary"
              >
                <span v-if="isFileOperationLoading">Uploading...</span>
                <span v-else>Upload File</span>
              </button>
              <button
                @click="clearFileSelection"
                :disabled="!selectedFile || isFileOperationLoading"
                class="btn btn-secondary"
              >
                Clear
              </button>
            </div>
          </div>

          <!-- File Download/Delete Section -->
          <div class="file-actions-section">
            <h4 class="section-title">Manage Existing Files</h4>
            <div class="form-group">
              <label for="action-drum-select" class="form-label">Select Drum:</label>
              <select
                id="action-drum-select"
                v-model="actionDrumId"
                class="form-select"
                :disabled="!statusStore.isConnected || isFileOperationLoading"
              >
                <option value="">Select a drum</option>
                <option v-for="drumId in availableDrums" :key="drumId" :value="drumId">
                  Drum {{ drumId }}
                </option>
              </select>
            </div>

            <div class="button-group">
              <button
                @click="downloadFile"
                :disabled="!statusStore.isConnected || actionDrumId === '' || isFileOperationLoading" 
                class="btn btn-secondary"
              >
                <span v-if="isFileOperationLoading">Downloading...</span>
                <span v-else>Download Geometry</span>
              </button>
              <button
                @click="deleteFile"
                :disabled="!statusStore.isConnected || !actionDrumId || isFileOperationLoading"
                class="btn btn-danger"
              >
                <span v-if="isFileOperationLoading">Deleting...</span>
                <span v-else>Delete Geometry</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Job Management -->
    <div class="control-card">
      <div class="card-header">
        <h3 class="card-title">Print Job Management</h3>
      </div>
      <div class="card-content">
        <div v-if="!statusStore.isConnected" class="disabled-overlay">
          <p class="disabled-text">Connect to recoater to manage print jobs</p>
        </div>

        <div class="job-status-section">
          <div class="status-display">
            <div class="status-item">
              <span class="status-label">Current State:</span>
              <span :class="['status-value', getJobStatusClass()]">
                {{ getJobStatusText() }}
              </span>
            </div>
            <div class="status-item">
              <span class="status-label">Print Active:</span>
              <span :class="['status-value', isPrinting ? 'status-active' : 'status-inactive']">
                {{ isPrinting ? 'Yes' : 'No' }}
              </span>
            </div>
            <div class="status-item">
              <span class="status-label">Has Errors:</span>
              <span :class="['status-value', hasJobError ? 'status-error' : 'status-ok']">
                {{ hasJobError ? 'Yes' : 'No' }}
              </span>
            </div>
          </div>

          <div class="job-controls">
            <button
              @click="startPrintJob"
              :disabled="!statusStore.isConnected || isPrinting || isJobOperationLoading"
              class="btn btn-success"
            >
              <span v-if="isJobOperationLoading">Starting...</span>
              <span v-else>Start Print Job</span>
            </button>
            <button
              @click="cancelPrintJob"
              :disabled="!statusStore.isConnected || !isPrinting || isJobOperationLoading"
              class="btn btn-danger"
            >
              <span v-if="isJobOperationLoading">Cancelling...</span>
              <span v-else>Cancel Print Job</span>
            </button>
            <button
              @click="refreshJobStatus"
              :disabled="!statusStore.isConnected || isJobOperationLoading"
              class="btn btn-secondary"
            >
              <span v-if="isJobOperationLoading">Refreshing...</span>
              <span v-else>Refresh Status</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Success/Error Messages -->
    <div v-if="successMessage" class="message message-success">
      {{ successMessage }}
    </div>
    <div v-if="errorMessage" class="message message-error">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStatusStore } from '../stores/status'
import apiService from '../services/api'

export default {
  name: 'PrintView',
  setup() {
    const statusStore = useStatusStore()
    
    // Reactive state
    const isLoading = ref(false)
    const previewLoading = ref(false)
    const previewImageUrl = ref(null)
    const previewSource = ref('layer')
    const successMessage = ref('')
    const errorMessage = ref('')
    
    // Layer parameters
    const layerParams = ref({
      filling_id: 1,
      speed: 30.0,
      powder_saving: true,
      x_offset: 0.0
    })

    // File management state
    const selectedFile = ref(null)
    const selectedDrumId = ref('')
    const actionDrumId = ref('')
    const isFileOperationLoading = ref(false)
    const fileInput = ref(null)

    // Job management state
    const isJobOperationLoading = ref(false)
    
    // Computed properties
    const isParametersValid = computed(() => {
      return layerParams.value.filling_id !== null &&
             layerParams.value.speed > 0
    })

    // Available drums (0, 1, 2 based on hardware limitation)
    const availableDrums = computed(() => [0, 1, 2])

    // Print job status computed properties
    const jobStatus = computed(() => statusStore.printData?.job_status || {})
    const isPrinting = computed(() => jobStatus.value.is_printing || false)
    const hasJobError = computed(() => jobStatus.value.has_error || false)

    const getJobStatusClass = () => {
      const state = jobStatus.value.state
      if (state === 'printing') return 'status-active'
      if (state === 'error') return 'status-error'
      return 'status-ready'
    }

    const getJobStatusText = () => {
      const state = jobStatus.value.state
      if (state === 'printing') return 'Printing'
      if (state === 'error') return 'Error'
      if (state === 'ready') return 'Ready'
      return 'Unknown'
    }
    
    // Methods
    const showMessage = (message, isError = false) => {
      if (isError) {
        errorMessage.value = message
        successMessage.value = ''
      } else {
        successMessage.value = message
        errorMessage.value = ''
      }
      
      // Auto-clear messages after 5 seconds
      setTimeout(() => {
        successMessage.value = ''
        errorMessage.value = ''
      }, 5000)
    }
    
    const loadParameters = async () => {
      if (!statusStore.isConnected) return
      
      isLoading.value = true
      try {
        const response = await apiService.getLayerParameters()
        layerParams.value = { ...response.data }
        showMessage('Layer parameters loaded successfully')
      } catch (error) {
        console.error('Failed to load layer parameters:', error)
        showMessage('Failed to load layer parameters: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isLoading.value = false
      }
    }
    
    const saveParameters = async () => {
      if (!statusStore.isConnected || !isParametersValid.value) return
      
      isLoading.value = true
      try {
        await apiService.setLayerParameters(layerParams.value)
        showMessage('Layer parameters saved successfully')
      } catch (error) {
        console.error('Failed to save layer parameters:', error)
        showMessage('Failed to save layer parameters: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isLoading.value = false
      }
    }
    
    const loadPreview = async () => {
      if (!statusStore.isConnected) return

      previewLoading.value = true
      try {
        let response
        let successMessage

        if (previewSource.value === 'layer') {
          // Load current layer configuration preview
          response = await apiService.getLayerPreview()
          successMessage = 'Layer configuration preview loaded successfully'
        } else if (previewSource.value.startsWith('drum-')) {
          // Load drum geometry preview
          const drumId = parseInt(previewSource.value.split('-')[1])
          response = await apiService.getDrumGeometryPreview(drumId)
          successMessage = `Drum ${drumId} geometry preview loaded successfully`
        } else {
          throw new Error('Invalid preview source selected')
        }

        // Create object URL from blob
        const blob = new Blob([response.data], { type: 'image/png' })
        if (previewImageUrl.value) {
          URL.revokeObjectURL(previewImageUrl.value)
        }
        previewImageUrl.value = URL.createObjectURL(blob)

        showMessage(successMessage)
      } catch (error) {
        console.error('Failed to load preview:', error)
        showMessage('Failed to load preview: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        previewLoading.value = false
      }
    }
    
    const handlePreviewError = () => {
      showMessage('Failed to display preview image', true)
      if (previewImageUrl.value) {
        URL.revokeObjectURL(previewImageUrl.value)
        previewImageUrl.value = null
      }
    }

    // File Management Methods
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      selectedFile.value = file
    }

    const clearFileSelection = () => {
      selectedFile.value = null
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const uploadFile = async () => {
      if (!selectedFile.value || selectedDrumId.value === '') {
        showMessage('Please select both a file and a drum', true)
        return
      }

      isFileOperationLoading.value = true
      try {
        await apiService.uploadDrumGeometry(selectedDrumId.value, selectedFile.value)
        showMessage(`File uploaded successfully to drum ${selectedDrumId.value}`)
        clearFileSelection()
      } catch (error) {
        console.error('Failed to upload file:', error)
        showMessage('Failed to upload file: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isFileOperationLoading.value = false
      }
    }

    const downloadFile = async () => {
      if (actionDrumId.value === '') {
        showMessage('Please select a drum', true)
        return
      }

      isFileOperationLoading.value = true
      try {
        const response = await apiService.downloadDrumGeometry(actionDrumId.value)

        // Create download link
        const blob = new Blob([response.data], { type: 'image/png' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `drum_${actionDrumId.value}_geometry.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        showMessage(`Geometry file downloaded from drum ${actionDrumId.value}`)
      } catch (error) {
        console.error('Failed to download file:', error)
        showMessage('Failed to download file: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isFileOperationLoading.value = false
      }
    }

    const deleteFile = async () => {
      if (actionDrumId.value === '') {
        showMessage('Please select a drum', true)
        return
      }

      if (!confirm(`Are you sure you want to delete the geometry file from drum ${actionDrumId.value}?`)) {
        return
      }

      isFileOperationLoading.value = true
      try {
        await apiService.deleteDrumGeometry(actionDrumId.value)
        showMessage(`Geometry file deleted from drum ${actionDrumId.value}`)
      } catch (error) {
        console.error('Failed to delete file:', error)
        showMessage('Failed to delete file: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isFileOperationLoading.value = false
      }
    }

    // Job Management Methods
    const startPrintJob = async () => {
      isJobOperationLoading.value = true
      try {
        await apiService.startPrintJob()
        showMessage('Print job started successfully')
      } catch (error) {
        console.error('Failed to start print job:', error)
        showMessage('Failed to start print job: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isJobOperationLoading.value = false
      }
    }

    const cancelPrintJob = async () => {
      if (!confirm('Are you sure you want to cancel the current print job?')) {
        return
      }

      isJobOperationLoading.value = true
      try {
        await apiService.cancelPrintJob()
        showMessage('Print job cancelled successfully')
      } catch (error) {
        console.error('Failed to cancel print job:', error)
        showMessage('Failed to cancel print job: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isJobOperationLoading.value = false
      }
    }

    const refreshJobStatus = async () => {
      isJobOperationLoading.value = true
      try {
        await apiService.getPrintJobStatus()
        showMessage('Job status refreshed')
      } catch (error) {
        console.error('Failed to refresh job status:', error)
        showMessage('Failed to refresh job status: ' + (error.response?.data?.detail || error.message), true)
      } finally {
        isJobOperationLoading.value = false
      }
    }

    // Lifecycle hooks
    onMounted(() => {
      statusStore.connectWebSocket()
      
      // Load initial data if connected
      if (statusStore.isConnected) {
        loadParameters()
      }
    })
    
    onUnmounted(() => {
      // Clean up object URL
      if (previewImageUrl.value) {
        URL.revokeObjectURL(previewImageUrl.value)
      }
    })
    
    return {
      statusStore,
      isLoading,
      previewLoading,
      previewImageUrl,
      previewSource,
      successMessage,
      errorMessage,
      layerParams,
      isParametersValid,

      // File management
      selectedFile,
      selectedDrumId,
      actionDrumId,
      isFileOperationLoading,
      fileInput,
      availableDrums,
      handleFileSelect,
      clearFileSelection,
      formatFileSize,
      uploadFile,
      downloadFile,
      deleteFile,

      // Job management
      isJobOperationLoading,
      jobStatus,
      isPrinting,
      hasJobError,
      getJobStatusClass,
      getJobStatusText,
      startPrintJob,
      cancelPrintJob,
      refreshJobStatus,

      // Existing methods
      loadParameters,
      saveParameters,
      loadPreview,
      handlePreviewError
    }
  }
}
</script>

<style scoped>
.print-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.view-title {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

/* Status Card */
.status-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-connected {
  background-color: #10b981;
}

.status-disconnected {
  background-color: #ef4444;
}

.status-text {
  font-weight: 500;
  color: #374151;
}

/* Control Cards */
.control-card {
  background: white;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  position: relative;
}

.card-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.card-title {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.card-subtitle {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 0.9rem;
  font-style: italic;
  line-height: 1.4;
}

.card-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
  position: relative;
}

.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 8px;
}

.disabled-text {
  color: #6b7280;
  font-weight: 500;
  text-align: center;
}

/* Parameter Grid */
.parameter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.parameter-group {
  display: flex;
  flex-direction: column;
}

.parameter-label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.parameter-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.parameter-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.parameter-input:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.parameter-checkbox {
  margin-right: 0.5rem;
}

.parameter-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
  margin-bottom: 0;
}

/* Preview Container */
.preview-container {
  min-height: 300px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  background: #f9fafb;
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.preview-image-container {
  max-width: 100%;
  max-height: 400px;
  overflow: hidden;
  border-radius: 6px;
}

.preview-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #9ca3af;
}

.preview-icon {
  width: 48px;
  height: 48px;
}

.preview-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.preview-controls .form-group {
  margin-bottom: 0;
}

/* Buttons */
.button-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
  border-color: #2563eb;
}

.btn-secondary {
  background-color: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

/* Messages */
.message {
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-weight: 500;
}

.message-success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.message-error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* File Management Styles */
.file-management-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.file-upload-section,
.file-actions-section {
  padding: 1rem;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.form-file-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
}

.file-info {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #e0f2fe;
  border: 1px solid #b3e5fc;
  border-radius: 4px;
  font-size: 0.85rem;
  color: #0277bd;
}

/* Job Management Styles */
.job-status-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.status-display {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.status-label {
  font-size: 0.85rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-value {
  font-size: 1rem;
  font-weight: 600;
}

.status-ready {
  color: #059669;
}

.status-active {
  color: #dc2626;
}

.status-error {
  color: #dc2626;
}

.status-inactive {
  color: #6b7280;
}

.status-ok {
  color: #059669;
}

.job-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-success {
  background-color: #059669;
  color: white;
  border: 1px solid #047857;
}

.btn-success:hover:not(:disabled) {
  background-color: #047857;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
  border: 1px solid #b91c1c;
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
}

/* Responsive Design */
@media (max-width: 768px) {
  .print-view {
    padding: 0.5rem;
  }

  .parameter-grid {
    grid-template-columns: 1fr;
  }

  .file-management-grid {
    grid-template-columns: 1fr;
  }

  .status-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .status-display {
    grid-template-columns: 1fr;
  }

  .button-group,
  .job-controls {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}
</style>
