/**
 * Tests for PrintView component
 * =============================
 * 
 * Comprehensive test suite for the PrintView component including
 * layer parameters management and preview functionality.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import PrintView from '../PrintView.vue'
import { useStatusStore } from '../../stores/status'
import apiService from '../../services/api'

// Mock API service
vi.mock('../../services/api', () => ({
  default: {
    getLayerParameters: vi.fn(),
    setLayerParameters: vi.fn(),
    getLayerPreview: vi.fn(),
    getDrumGeometryPreview: vi.fn(),
    startPrintJob: vi.fn(),
    cancelPrintJob: vi.fn(),
    getPrintJobStatus: vi.fn(),
    uploadDrumGeometry: vi.fn(),
    downloadDrumGeometry: vi.fn(),
    deleteDrumGeometry: vi.fn()
  }
}))

// Mock status store
vi.mock('../../stores/status', () => ({
  useStatusStore: vi.fn()
}))

describe('PrintView', () => {
  let mockStatusStore

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    // Mock URL APIs for blob handling
    global.URL = global.URL || {}
    global.URL.createObjectURL = vi.fn(() => 'blob:mock-url')
    global.URL.revokeObjectURL = vi.fn()

    mockStatusStore = {
      isConnected: false,
      printData: null,
      connectWebSocket: vi.fn(),
      disconnectWebSocket: vi.fn()
    }

    useStatusStore.mockReturnValue(mockStatusStore)
  })

  describe('Component Rendering', () => {
    it('renders the print view with all sections', () => {
      const wrapper = mount(PrintView)

      expect(wrapper.find('.print-view').exists()).toBe(true)
      expect(wrapper.find('.view-title').text()).toBe('Print Control')
      expect(wrapper.find('.status-card').exists()).toBe(true)
      expect(wrapper.find('.control-card').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer Parameters')
      expect(wrapper.text()).toContain('Layer Preview')
    })

    it('shows disconnected status when not connected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      expect(wrapper.find('.status-disconnected').exists()).toBe(true)
      expect(wrapper.find('.status-text').text()).toBe('Disconnected')
    })

    it('shows connected status when connected', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('.status-connected').exists()).toBe(true)
      expect(wrapper.find('.status-text').text()).toBe('Connected')
    })

    it('shows disabled overlay when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const overlays = wrapper.findAll('.disabled-overlay')
      expect(overlays.length).toBeGreaterThan(0)
      expect(wrapper.text()).toContain('Connect to recoater to configure layer parameters')
      expect(wrapper.text()).toContain('Connect to recoater to view layer preview')
    })
  })

  describe('Layer Parameters', () => {
    it('renders all parameter input fields', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').exists()).toBe(true)
      expect(wrapper.find('#speed').exists()).toBe(true)
      expect(wrapper.find('#x-offset').exists()).toBe(true)
      expect(wrapper.find('.parameter-checkbox').exists()).toBe(true)
    })

    it('disables inputs when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').attributes('disabled')).toBeDefined()
      expect(wrapper.find('#speed').attributes('disabled')).toBeDefined()
      expect(wrapper.find('#x-offset').attributes('disabled')).toBeDefined()
      expect(wrapper.find('.parameter-checkbox').attributes('disabled')).toBeDefined()
    })

    it('enables inputs when connected', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('#speed').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('#x-offset').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('.parameter-checkbox').attributes('disabled')).toBeUndefined()
    })

    it('calls loadParameters when Load Current button is clicked', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: {
          filling_id: 2,
          speed: 25.0,
          powder_saving: false,
          x_offset: 5.0
        }
      })

      const wrapper = mount(PrintView)
      // Clear the call from onMounted
      apiService.getLayerParameters.mockClear()

      const loadButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Current'))

      await loadButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getLayerParameters).toHaveBeenCalledOnce()
    })

    it('calls saveParameters when Save Parameters button is clicked', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)
      const saveButton = wrapper.findAll('button').find(btn => btn.text().includes('Save Parameters'))
      
      await saveButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.setLayerParameters).toHaveBeenCalledOnce()
    })

    it('validates parameters before saving', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      // Set invalid speed
      wrapper.vm.layerParams.speed = 0
      expect(wrapper.vm.isParametersValid).toBe(false)

      // Set valid speed
      wrapper.vm.layerParams.speed = 30
      expect(wrapper.vm.isParametersValid).toBe(true)
    })

    it('disables save button when parameters are invalid', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: {
          filling_id: 1,
          speed: 30.0,
          powder_saving: true,
          x_offset: 0.0
        }
      })

      const wrapper = mount(PrintView)
      await wrapper.vm.$nextTick()

      // Set invalid parameters
      wrapper.vm.layerParams.speed = 0
      await wrapper.vm.$nextTick()

      // Find save button by looking for the button that calls saveParameters
      const saveButton = wrapper.find('button[class*="btn-primary"]')
      expect(saveButton.exists()).toBe(true)
      expect(saveButton.attributes('disabled')).toBeDefined()
    })
  })

  describe('Layer Preview', () => {
    it('shows preview placeholder when no image is loaded', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('.preview-placeholder').exists()).toBe(true)
      expect(wrapper.find('.preview-icon').exists()).toBe(true)
      expect(wrapper.text()).toContain('No preview available')
    })

    it('shows loading state when preview is loading', async () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      wrapper.vm.previewLoading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.preview-loading').exists()).toBe(true)
      expect(wrapper.find('.loading-spinner').exists()).toBe(true)
      expect(wrapper.text()).toContain('Loading preview...')
    })

    it('calls loadPreview when Load Preview button is clicked', async () => {
      mockStatusStore.isConnected = true
      
      // Mock blob response
      const mockBlob = new Blob(['mock image data'], { type: 'image/png' })
      apiService.getLayerPreview.mockResolvedValue({ data: mockBlob })

      const wrapper = mount(PrintView)
      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      
      await previewButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getLayerPreview).toHaveBeenCalledOnce()
    })

    it('disables preview button when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      expect(previewButton.attributes('disabled')).toBeDefined()
    })

    it('has preview source selector with correct options', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      const previewSelect = wrapper.find('#preview-drum-select')
      expect(previewSelect.exists()).toBe(true)

      const options = previewSelect.findAll('option')
      expect(options).toHaveLength(4) // 1 layer option + 3 drum options
      expect(options[0].text()).toBe('Current Layer Configuration')
      expect(options[1].text()).toBe('Drum 0 Geometry')
      expect(options[2].text()).toBe('Drum 1 Geometry')
      expect(options[3].text()).toBe('Drum 2 Geometry')
    })

    it('calls getDrumGeometryPreview when drum geometry is selected', async () => {
      mockStatusStore.isConnected = true

      // Mock blob response
      const mockBlob = new Blob(['mock drum image data'], { type: 'image/png' })
      apiService.getDrumGeometryPreview.mockResolvedValue({ data: mockBlob })

      const wrapper = mount(PrintView)

      // Select drum 1 geometry
      const previewSelect = wrapper.find('#preview-drum-select')
      await previewSelect.setValue('drum-1')

      // Click load preview
      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      await previewButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getDrumGeometryPreview).toHaveBeenCalledWith(1)
    })
  })

  describe('Error Handling', () => {
    it('shows error message when parameter loading fails', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockRejectedValue(new Error('Connection failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.loadParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to load layer parameters')
    })

    it('shows error message when parameter saving fails', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockRejectedValue(new Error('Save failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.saveParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to save layer parameters')
    })

    it('shows error message when preview loading fails', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerPreview.mockRejectedValue(new Error('Preview failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.loadPreview()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to load layer preview')
    })
  })

  describe('Success Messages', () => {
    it('shows success message when parameters are loaded successfully', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: { filling_id: 1, speed: 30.0, powder_saving: true, x_offset: 0.0 }
      })

      const wrapper = mount(PrintView)
      await wrapper.vm.loadParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-success').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer parameters loaded successfully')
    })

    it('shows success message when parameters are saved successfully', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)
      await wrapper.vm.saveParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-success').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer parameters saved successfully')
    })
  })

  describe('WebSocket Integration', () => {
    it('connects to WebSocket on mount', () => {
      mount(PrintView)
      expect(mockStatusStore.connectWebSocket).toHaveBeenCalledOnce()
    })
  })

  describe('Component Lifecycle', () => {
    it('cleans up object URLs on unmount', () => {
      // Mock URL.revokeObjectURL before mounting
      global.URL = global.URL || {}
      global.URL.revokeObjectURL = vi.fn()

      const wrapper = mount(PrintView)

      // Set a preview URL
      wrapper.vm.previewImageUrl = 'blob:mock-url'

      wrapper.unmount()

      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url')
    })
  })

  describe('File Management', () => {
    beforeEach(() => {
      mockStatusStore.isConnected = true
    })

    it('renders file management section when connected', () => {
      const wrapper = mount(PrintView)

      expect(wrapper.find('.file-management-grid').exists()).toBe(true)
      expect(wrapper.find('.file-upload-section').exists()).toBe(true)
      expect(wrapper.find('.file-actions-section').exists()).toBe(true)
    })

    it('shows disabled overlay when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const controlCards = wrapper.findAll('.control-card')
      const fileManagementCard = controlCards.find(card =>
        card.text().includes('File Management')
      )
      expect(fileManagementCard.find('.disabled-overlay').exists()).toBe(true)
    })

    it('handles file selection', async () => {
      const wrapper = mount(PrintView)

      const fileInput = wrapper.find('#file-input')
      expect(fileInput.exists()).toBe(true)

      // Mock file
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' })

      // Simulate file selection
      Object.defineProperty(fileInput.element, 'files', {
        value: [mockFile],
        writable: false
      })

      await fileInput.trigger('change')

      expect(wrapper.vm.selectedFile).toBe(mockFile)
    })

    it('uploads file successfully', async () => {
      apiService.uploadDrumGeometry.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)

      // Set up file and drum selection
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' })
      wrapper.vm.selectedFile = mockFile
      wrapper.vm.selectedDrumId = '1'

      await wrapper.vm.uploadFile()

      expect(apiService.uploadDrumGeometry).toHaveBeenCalledWith('1', mockFile)
      expect(wrapper.vm.selectedFile).toBe(null)
    })

    it('downloads file successfully', async () => {
      const mockBlob = new Blob(['test'], { type: 'image/png' })
      apiService.downloadDrumGeometry.mockResolvedValue({ data: mockBlob })

      // Mock DOM methods
      const mockLink = {
        href: '',
        download: '',
        click: vi.fn(),
        setAttribute: vi.fn()
      }
      const originalCreateElement = document.createElement
      const originalAppendChild = document.body.appendChild
      const originalRemoveChild = document.body.removeChild

      document.createElement = vi.fn(() => mockLink)
      document.body.appendChild = vi.fn()
      document.body.removeChild = vi.fn()

      const wrapper = mount(PrintView)
      wrapper.vm.actionDrumId = '1'

      await wrapper.vm.downloadFile()

      expect(apiService.downloadDrumGeometry).toHaveBeenCalledWith('1')
      expect(mockLink.click).toHaveBeenCalled()

      // Restore original methods
      document.createElement = originalCreateElement
      document.body.appendChild = originalAppendChild
      document.body.removeChild = originalRemoveChild
    })

    it('deletes file with confirmation', async () => {
      apiService.deleteDrumGeometry.mockResolvedValue({ data: { success: true } })

      // Mock confirm dialog
      global.confirm = vi.fn(() => true)

      const wrapper = mount(PrintView)
      wrapper.vm.actionDrumId = '1'

      await wrapper.vm.deleteFile()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.deleteDrumGeometry).toHaveBeenCalledWith('1')
    })

    it('cancels file deletion when not confirmed', async () => {
      // Mock confirm dialog to return false
      global.confirm = vi.fn(() => false)

      const wrapper = mount(PrintView)
      wrapper.vm.actionDrumId = '1'

      await wrapper.vm.deleteFile()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.deleteDrumGeometry).not.toHaveBeenCalled()
    })
  })

  describe('Job Management', () => {
    beforeEach(() => {
      mockStatusStore.isConnected = true
      mockStatusStore.printData = {
        job_status: {
          state: 'ready',
          is_printing: false,
          has_error: false
        }
      }
    })

    it('renders job management section when connected', () => {
      const wrapper = mount(PrintView)

      expect(wrapper.find('.job-status-section').exists()).toBe(true)
      expect(wrapper.find('.status-display').exists()).toBe(true)
      expect(wrapper.find('.job-controls').exists()).toBe(true)
    })

    it('displays job status correctly', () => {
      const wrapper = mount(PrintView)

      expect(wrapper.vm.getJobStatusText()).toBe('Ready')
      expect(wrapper.vm.getJobStatusClass()).toBe('status-ready')
      expect(wrapper.vm.isPrinting).toBe(false)
      expect(wrapper.vm.hasJobError).toBe(false)
    })

    it('displays printing status correctly', () => {
      mockStatusStore.printData.job_status = {
        state: 'printing',
        is_printing: true,
        has_error: false
      }

      const wrapper = mount(PrintView)

      expect(wrapper.vm.getJobStatusText()).toBe('Printing')
      expect(wrapper.vm.getJobStatusClass()).toBe('status-active')
      expect(wrapper.vm.isPrinting).toBe(true)
    })

    it('displays error status correctly', () => {
      mockStatusStore.printData.job_status = {
        state: 'error',
        is_printing: false,
        has_error: true
      }

      const wrapper = mount(PrintView)

      expect(wrapper.vm.getJobStatusText()).toBe('Error')
      expect(wrapper.vm.getJobStatusClass()).toBe('status-error')
      expect(wrapper.vm.hasJobError).toBe(true)
    })

    it('starts print job successfully', async () => {
      apiService.startPrintJob.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)

      await wrapper.vm.startPrintJob()

      expect(apiService.startPrintJob).toHaveBeenCalled()
    })

    it('cancels print job with confirmation', async () => {
      apiService.cancelPrintJob.mockResolvedValue({ data: { success: true } })
      global.confirm = vi.fn(() => true)

      const wrapper = mount(PrintView)

      await wrapper.vm.cancelPrintJob()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.cancelPrintJob).toHaveBeenCalled()
    })

    it('does not cancel print job when not confirmed', async () => {
      global.confirm = vi.fn(() => false)

      const wrapper = mount(PrintView)

      await wrapper.vm.cancelPrintJob()

      expect(global.confirm).toHaveBeenCalled()
      expect(apiService.cancelPrintJob).not.toHaveBeenCalled()
    })

    it('refreshes job status', async () => {
      apiService.getPrintJobStatus.mockResolvedValue({ data: { state: 'ready' } })

      const wrapper = mount(PrintView)

      await wrapper.vm.refreshJobStatus()

      expect(apiService.getPrintJobStatus).toHaveBeenCalled()
    })

    it('disables start button when printing', () => {
      mockStatusStore.printData.job_status.is_printing = true

      const wrapper = mount(PrintView)

      const buttons = wrapper.findAll('button')
      const startButton = buttons.find(btn =>
        btn.text().includes('Start Print Job')
      )
      expect(startButton.element.disabled).toBe(true)
    })

    it('disables cancel button when not printing', () => {
      mockStatusStore.printData.job_status.is_printing = false

      const wrapper = mount(PrintView)

      const buttons = wrapper.findAll('button')
      const cancelButton = buttons.find(btn =>
        btn.text().includes('Cancel Print Job')
      )
      expect(cancelButton.element.disabled).toBe(true)
    })
  })
})
