<template>
  <div class="leveler-control bg-white rounded-lg shadow-md p-6 border border-gray-200">
    <!-- Header -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-800">Leveler Control</h3>
      <div class="flex items-center space-x-2">
        <div
          :class="[
            'w-3 h-3 rounded-full',
            connected ? 'bg-green-500' : 'bg-red-500'
          ]"
        ></div>
        <span class="text-sm font-medium" :class="connected ? 'text-green-600' : 'text-red-600'">
          {{ connected ? 'Connected' : 'Disconnected' }}
        </span>
      </div>
    </div>

    <!-- Pressure Control Section -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-3">Pressure Control</h4>
      
      <!-- Pressure Status Display -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="text-xs font-medium text-gray-500 uppercase tracking-wide">Current</div>
          <div class="text-lg font-semibold text-gray-900">
            {{ pressureData?.value?.toFixed(2) || '--' }} Pa
          </div>
        </div>
        <div class="bg-blue-50 rounded-lg p-3">
          <div class="text-xs font-medium text-blue-500 uppercase tracking-wide">Target</div>
          <div class="text-lg font-semibold text-blue-900">
            {{ pressureData?.target?.toFixed(2) || '--' }} Pa
          </div>
        </div>
        <div class="bg-orange-50 rounded-lg p-3">
          <div class="text-xs font-medium text-orange-500 uppercase tracking-wide">Maximum</div>
          <div class="text-lg font-semibold text-orange-900">
            {{ pressureData?.maximum?.toFixed(2) || '--' }} Pa
          </div>
        </div>
      </div>

      <!-- Pressure Input and Control -->
      <div class="flex items-end space-x-3">
        <div class="flex-1">
          <label for="target-pressure" class="block text-sm font-medium text-gray-700 mb-1">
            Target Pressure (Pa)
          </label>
          <input
            id="target-pressure"
            v-model.number="targetPressure"
            type="number"
            step="0.1"
            min="0"
            :max="pressureData?.maximum || 10"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            :disabled="!connected || isSettingPressure"
            placeholder="Enter target pressure"
          />
        </div>
        <button
          @click="setPressure"
          :disabled="!connected || isSettingPressure || !isValidPressure"
          class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isSettingPressure" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Setting...
          </span>
          <span v-else>Set Pressure</span>
        </button>
      </div>
    </div>

    <!-- Sensor Status Section -->
    <div class="border-t border-gray-200 pt-4">
      <h4 class="text-md font-medium text-gray-700 mb-3">Magnetic Sensor</h4>
      
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div
            :class="[
              'w-4 h-4 rounded-full',
              sensorData?.state ? 'bg-green-500' : 'bg-gray-400'
            ]"
          ></div>
          <span class="text-sm font-medium text-gray-700">
            Sensor State: {{ sensorData?.state ? 'Active' : 'Inactive' }}
          </span>
        </div>
        
        <div class="text-xs text-gray-500">
          {{ sensorData?.state ? 'Magnetic field detected' : 'No magnetic field' }}
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="errorMessage" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error</h3>
          <div class="mt-1 text-sm text-red-700">{{ errorMessage }}</div>
        </div>
        <div class="ml-auto pl-3">
          <button @click="errorMessage = ''" class="text-red-400 hover:text-red-600">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import apiService from '../services/api'

export default {
  name: 'LevelerControl',
  props: {
    pressureData: {
      type: Object,
      default: () => ({})
    },
    sensorData: {
      type: Object,
      default: () => ({})
    },
    connected: {
      type: Boolean,
      default: false
    }
  },
  emits: ['error', 'pressure-set'],
  setup(props, { emit }) {
    const targetPressure = ref('')
    const isSettingPressure = ref(false)
    const errorMessage = ref('')

    // Computed properties
    const isValidPressure = computed(() => {
      const pressure = parseFloat(targetPressure.value)
      return !isNaN(pressure) && 
             pressure >= 0 && 
             pressure <= (props.pressureData?.maximum || 10)
    })

    // Auto-clear error messages
    watch(errorMessage, (newError) => {
      if (newError) {
        setTimeout(() => {
          errorMessage.value = ''
        }, 5000)
      }
    })

    // Methods
    const setPressure = async () => {
      if (!isValidPressure.value || isSettingPressure.value) {
        return
      }

      isSettingPressure.value = true
      errorMessage.value = ''

      try {
        const pressure = parseFloat(targetPressure.value)
        const response = await apiService.setLevelerPressure(pressure)
        
        emit('pressure-set', {
          target: pressure,
          response: response.data
        })
        
        // Clear the input after successful set
        targetPressure.value = ''
        
      } catch (error) {
        console.error('Error setting leveler pressure:', error)
        errorMessage.value = error.response?.data?.detail || 'Failed to set leveler pressure'
        emit('error', error)
      } finally {
        isSettingPressure.value = false
      }
    }

    return {
      targetPressure,
      isSettingPressure,
      errorMessage,
      isValidPressure,
      setPressure
    }
  }
}
</script>

<style scoped>
/* Component-specific styles */
.leveler-control {
  transition: all 0.2s ease-in-out;
}

.leveler-control:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
