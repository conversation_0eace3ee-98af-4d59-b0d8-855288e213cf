<template>
  <div class="drum-control-card bg-white rounded-lg shadow-md p-6 border border-gray-200">
    <!-- Drum Header -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-800">
        Drum {{ drumId }}
      </h3>
      <div class="flex items-center space-x-2">
        <div 
          :class="[
            'w-3 h-3 rounded-full',
            drumStatus?.running ? 'bg-green-500' : 'bg-gray-400'
          ]"
          :title="drumStatus?.running ? 'Running' : 'Stopped'"
        ></div>
        <span class="text-sm text-gray-600">
          {{ drumStatus?.running ? 'Running' : 'Stopped' }}
        </span>
      </div>
    </div>

    <!-- Drum Information -->
    <div class="grid grid-cols-2 gap-4 mb-6">
      <div class="bg-gray-50 p-3 rounded">
        <div class="text-sm text-gray-600">Position</div>
        <div class="text-lg font-mono">
          {{ drumStatus?.position?.toFixed(2) || '0.00' }} mm
        </div>
      </div>
      <div class="bg-gray-50 p-3 rounded">
        <div class="text-sm text-gray-600">Circumference</div>
        <div class="text-lg font-mono">
          {{ drumStatus?.circumference?.toFixed(2) || '0.00' }} mm
        </div>
      </div>
    </div>

    <!-- Motion Controls -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-3">Motion Control</h4>
      
      <!-- Motion Parameters -->
      <div class="grid grid-cols-3 gap-3 mb-4">
        <div>
          <label class="block text-sm text-gray-600 mb-1">Mode</label>
          <select 
            v-model="motionParams.mode" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            :disabled="!connected"
          >
            <option value="relative">Relative</option>
            <option value="absolute">Absolute</option>
            <option value="turns">Turns</option>
            <option value="speed">Speed</option>
            <option value="homing">Homing</option>
          </select>
        </div>
        <div>
          <label class="block text-sm text-gray-600 mb-1">Speed (mm/s)</label>
          <input 
            v-model.number="motionParams.speed" 
            type="number" 
            min="0.1" 
            step="0.1"
            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            :disabled="!connected"
          />
        </div>
        <div v-if="motionParams.mode === 'relative' || motionParams.mode === 'absolute'">
          <label class="block text-sm text-gray-600 mb-1">Distance (mm)</label>
          <input 
            v-model.number="motionParams.distance" 
            type="number" 
            step="0.1"
            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            :disabled="!connected"
          />
        </div>
        <div v-if="motionParams.mode === 'turns'">
          <label class="block text-sm text-gray-600 mb-1">Turns</label>
          <input 
            v-model.number="motionParams.turns" 
            type="number" 
            min="0.1" 
            step="0.1"
            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            :disabled="!connected"
          />
        </div>
      </div>

      <!-- Motion Buttons -->
      <div class="flex space-x-2">
        <button 
          @click="startMotion"
          :disabled="!connected || drumStatus?.running"
          class="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
        >
          Start Motion
        </button>
        <button 
          @click="cancelMotion"
          :disabled="!connected || !drumStatus?.running"
          class="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-gray-300 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
        >
          Cancel Motion
        </button>
      </div>
    </div>

    <!-- Pressure Controls -->
    <div class="grid grid-cols-2 gap-4">
      <!-- Ejection Pressure -->
      <div class="bg-blue-50 p-4 rounded-lg">
        <h5 class="text-sm font-medium text-blue-800 mb-2">Ejection Pressure</h5>
        <div class="space-y-2">
          <div class="text-xs text-blue-600">
            Current: {{ ejectionData?.value?.toFixed(1) || '0.0' }} {{ ejectionData?.unit || 'Pa' }}
          </div>
          <div class="text-xs text-blue-600">
            Target: {{ ejectionData?.target?.toFixed(1) || '0.0' }} {{ ejectionData?.unit || 'Pa' }}
          </div>
          <div class="flex space-x-1">
            <input 
              v-model.number="ejectionTarget" 
              type="number" 
              min="0" 
              step="1"
              class="flex-1 px-2 py-1 border border-blue-300 rounded text-xs"
              :disabled="!connected"
              placeholder="Target"
            />
            <select 
              v-model="ejectionUnit" 
              class="px-2 py-1 border border-blue-300 rounded text-xs"
              :disabled="!connected"
            >
              <option value="pascal">Pa</option>
              <option value="bar">bar</option>
            </select>
            <button 
              @click="setEjectionPressure"
              :disabled="!connected"
              class="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white px-2 py-1 rounded text-xs"
            >
              Set
            </button>
          </div>
        </div>
      </div>

      <!-- Suction Pressure -->
      <div class="bg-green-50 p-4 rounded-lg">
        <h5 class="text-sm font-medium text-green-800 mb-2">Suction Pressure</h5>
        <div class="space-y-2">
          <div class="text-xs text-green-600">
            Current: {{ suctionData?.value?.toFixed(1) || '0.0' }} Pa
          </div>
          <div class="text-xs text-green-600">
            Target: {{ suctionData?.target?.toFixed(1) || '0.0' }} Pa
          </div>
          <div class="flex space-x-1">
            <input 
              v-model.number="suctionTarget" 
              type="number" 
              min="0" 
              step="0.1"
              class="flex-1 px-2 py-1 border border-green-300 rounded text-xs"
              :disabled="!connected"
              placeholder="Target"
            />
            <button 
              @click="setSuctionPressure"
              :disabled="!connected"
              class="bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white px-2 py-1 rounded text-xs"
            >
              Set
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="errorMessage" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
      <div class="text-sm text-red-600">{{ errorMessage }}</div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import apiService from '../services/api'

export default {
  name: 'DrumControl',
  props: {
    drumId: {
      type: Number,
      required: true
    },
    drumStatus: {
      type: Object,
      default: () => ({})
    },
    motionData: {
      type: Object,
      default: () => ({})
    },
    ejectionData: {
      type: Object,
      default: () => ({})
    },
    suctionData: {
      type: Object,
      default: () => ({})
    },
    connected: {
      type: Boolean,
      default: false
    }
  },
  emits: ['error', 'motion-started', 'motion-cancelled', 'pressure-set'],
  setup(props, { emit }) {
    // Motion parameters
    const motionParams = ref({
      mode: 'relative',
      speed: 30.0,
      distance: 100.0,
      turns: 1.0
    })

    // Pressure parameters
    const ejectionTarget = ref(200.0)
    const ejectionUnit = ref('pascal')
    const suctionTarget = ref(2.0)

    // Error handling
    const errorMessage = ref('')

    // Clear error after 5 seconds
    watch(errorMessage, (newError) => {
      if (newError) {
        setTimeout(() => {
          errorMessage.value = ''
        }, 5000)
      }
    })

    // Motion control methods
    const startMotion = async () => {
      try {
        console.log(`Starting motion for drum ${props.drumId}:`, motionParams.value)
        
        const motionData = {
          mode: motionParams.value.mode,
          speed: motionParams.value.speed
        }

        if (motionParams.value.mode === 'relative' || motionParams.value.mode === 'absolute') {
          motionData.distance = motionParams.value.distance
        } else if (motionParams.value.mode === 'turns') {
          motionData.turns = motionParams.value.turns
        }

        await apiService.setDrumMotion(props.drumId, motionData)
        emit('motion-started', { drumId: props.drumId, motion: motionData })
        errorMessage.value = ''
      } catch (error) {
        console.error(`Error starting motion for drum ${props.drumId}:`, error)
        errorMessage.value = error.response?.data?.detail || 'Failed to start motion'
        emit('error', error)
      }
    }

    const cancelMotion = async () => {
      try {
        console.log(`Cancelling motion for drum ${props.drumId}`)
        await apiService.cancelDrumMotion(props.drumId)
        emit('motion-cancelled', { drumId: props.drumId })
        errorMessage.value = ''
      } catch (error) {
        console.error(`Error cancelling motion for drum ${props.drumId}:`, error)
        errorMessage.value = error.response?.data?.detail || 'Failed to cancel motion'
        emit('error', error)
      }
    }

    // Pressure control methods
    const setEjectionPressure = async () => {
      try {
        console.log(`Setting ejection pressure for drum ${props.drumId}:`, ejectionTarget.value, ejectionUnit.value)
        
        const ejectionData = {
          target: ejectionTarget.value,
          unit: ejectionUnit.value
        }

        await apiService.setDrumEjection(props.drumId, ejectionData)
        emit('pressure-set', { drumId: props.drumId, type: 'ejection', data: ejectionData })
        errorMessage.value = ''
      } catch (error) {
        console.error(`Error setting ejection pressure for drum ${props.drumId}:`, error)
        errorMessage.value = error.response?.data?.detail || 'Failed to set ejection pressure'
        emit('error', error)
      }
    }

    const setSuctionPressure = async () => {
      try {
        console.log(`Setting suction pressure for drum ${props.drumId}:`, suctionTarget.value)
        
        const suctionData = {
          target: suctionTarget.value
        }

        await apiService.setDrumSuction(props.drumId, suctionData)
        emit('pressure-set', { drumId: props.drumId, type: 'suction', data: suctionData })
        errorMessage.value = ''
      } catch (error) {
        console.error(`Error setting suction pressure for drum ${props.drumId}:`, error)
        errorMessage.value = error.response?.data?.detail || 'Failed to set suction pressure'
        emit('error', error)
      }
    }

    return {
      motionParams,
      ejectionTarget,
      ejectionUnit,
      suctionTarget,
      errorMessage,
      startMotion,
      cancelMotion,
      setEjectionPressure,
      setSuctionPressure
    }
  }
}
</script>

<style scoped>
.drum-control-card {
  min-height: 400px;
}
</style>
