/**
 * Test suite for LevelerControl component.
 * 
 * This module tests the leveler control component to ensure it renders correctly
 * and handles user interactions properly with mocked API services.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import LevelerControl from '../LevelerControl.vue'

// Mock the API service
vi.mock('../../services/api', () => ({
  default: {
    setLevelerPressure: vi.fn()
  }
}))

describe('LevelerControl', () => {
  let wrapper

  const defaultProps = {
    pressureData: {
      maximum: 10.0,
      target: 5.0,
      value: 4.8
    },
    sensorData: {
      state: true
    },
    connected: true
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('renders correctly with pressure and sensor data', () => {
    wrapper = mount(LevelerControl, {
      props: defaultProps
    })

    expect(wrapper.find('h3').text()).toBe('Leveler Control')
    expect(wrapper.text()).toContain('4.80 Pa') // Current pressure
    expect(wrapper.text()).toContain('5.00 Pa') // Target pressure
    expect(wrapper.text()).toContain('10.00 Pa') // Maximum pressure
    expect(wrapper.text()).toContain('Sensor State: Active')
  })

  it('shows disconnected state when not connected', () => {
    wrapper = mount(LevelerControl, {
      props: {
        ...defaultProps,
        connected: false
      }
    })

    expect(wrapper.text()).toContain('Disconnected')
    expect(wrapper.find('button').attributes('disabled')).toBeDefined()
  })

  it('shows inactive sensor state correctly', () => {
    wrapper = mount(LevelerControl, {
      props: {
        ...defaultProps,
        sensorData: { state: false }
      }
    })

    expect(wrapper.text()).toContain('Sensor State: Inactive')
    expect(wrapper.text()).toContain('No magnetic field')
  })

  it('handles empty pressure data gracefully', () => {
    wrapper = mount(LevelerControl, {
      props: {
        ...defaultProps,
        pressureData: {}
      }
    })

    expect(wrapper.text()).toContain('-- Pa') // Should show placeholder values
  })

  it('validates pressure input correctly', async () => {
    wrapper = mount(LevelerControl, {
      props: defaultProps
    })

    const input = wrapper.find('input[type="number"]')
    const button = wrapper.find('button')

    // Test valid pressure
    await input.setValue('7.5')
    expect(button.attributes('disabled')).toBeUndefined()

    // Test invalid pressure (negative)
    await input.setValue('-1')
    expect(button.attributes('disabled')).toBeDefined()

    // Test invalid pressure (exceeds maximum)
    await input.setValue('15')
    expect(button.attributes('disabled')).toBeDefined()
  })

  it('emits pressure-set event when pressure is set successfully', async () => {
    const mockApiService = await import('../../services/api')
    mockApiService.default.setLevelerPressure.mockResolvedValue({
      data: { success: true, target_pressure: 7.5 }
    })

    wrapper = mount(LevelerControl, {
      props: defaultProps
    })

    const input = wrapper.find('input[type="number"]')
    const button = wrapper.find('button')

    await input.setValue('7.5')
    await button.trigger('click')

    // Wait for async operation
    await wrapper.vm.$nextTick()

    expect(mockApiService.default.setLevelerPressure).toHaveBeenCalledWith(7.5)
    expect(wrapper.emitted('pressure-set')).toBeTruthy()
    expect(wrapper.emitted('pressure-set')[0][0]).toEqual({
      target: 7.5,
      response: { success: true, target_pressure: 7.5 }
    })
  })

  it('handles API errors gracefully', async () => {
    const mockApiService = await import('../../services/api')
    const mockError = new Error('API Error')
    mockError.response = { data: { detail: 'Connection failed' } }
    mockApiService.default.setLevelerPressure.mockRejectedValue(mockError)

    wrapper = mount(LevelerControl, {
      props: defaultProps
    })

    const input = wrapper.find('input[type="number"]')
    const button = wrapper.find('button')

    await input.setValue('7.5')
    await button.trigger('click')

    // Wait for async operation
    await wrapper.vm.$nextTick()

    expect(wrapper.emitted('error')).toBeTruthy()
    expect(wrapper.text()).toContain('Connection failed')
  })

  it('disables controls when setting pressure', async () => {
    const mockApiService = await import('../../services/api')
    mockApiService.default.setLevelerPressure.mockResolvedValue({
      data: { success: true, target_pressure: 7.5 }
    })

    wrapper = mount(LevelerControl, {
      props: defaultProps
    })

    const input = wrapper.find('input[type="number"]')
    const button = wrapper.find('button')

    await input.setValue('7.5')

    // Start the async operation
    const clickPromise = button.trigger('click')

    // Check that controls are disabled during operation (immediately after click)
    await wrapper.vm.$nextTick()
    expect(input.attributes('disabled')).toBeDefined()
    expect(button.attributes('disabled')).toBeDefined()
    expect(button.text()).toContain('Setting...')

    // Wait for the async operation to complete
    await clickPromise
    await wrapper.vm.$nextTick()

    // Check that controls are re-enabled
    expect(input.attributes('disabled')).toBeFalsy()
    expect(button.attributes('disabled')).toBeFalsy()
    expect(button.text()).toBe('Set Pressure')
  })
})
