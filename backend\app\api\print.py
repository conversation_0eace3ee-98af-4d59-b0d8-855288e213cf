"""
Print API Router
===============

This module provides FastAPI endpoints for print control operations including
layer parameters management and preview functionality.

The endpoints follow the openapi.json specification for print-related operations.
"""

from fastapi import APIRouter, HTTPException, Depends, Response, UploadFile, File, Path
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
import logging

from app.dependencies import get_recoater_client
from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/print", tags=["print"])


# Pydantic Models for Request/Response Validation

class LayerParametersRequest(BaseModel):
    """Request model for setting layer parameters."""
    filling_id: int = Field(..., description="The ID of the drum with the filling material powder. Set to -1 for no filling.")
    speed: float = Field(..., gt=0, description="The patterning speed [mm/s]")
    powder_saving: bool = Field(True, description="Flag indicating if powder saving strategies are used")
    x_offset: Optional[float] = Field(None, ge=0, description="The offset along the X axis [mm]")


class LayerParametersResponse(BaseModel):
    """Response model for layer parameters."""
    filling_id: int
    speed: float
    powder_saving: bool
    x_offset: Optional[float] = None
    max_x_offset: Optional[float] = None


class PrintJobResponse(BaseModel):
    """Response model for print job operations."""
    success: bool
    status: str
    job_id: Optional[str] = None


class FileUploadResponse(BaseModel):
    """Response model for file upload operations."""
    success: bool
    message: str
    drum_id: int
    file_size: int
    content_type: str


class FileDeleteResponse(BaseModel):
    """Response model for file deletion operations."""
    success: bool
    message: str
    drum_id: int


class PrintJobStatusResponse(BaseModel):
    """Response model for print job status."""
    state: str
    is_printing: bool
    has_error: bool


# API Endpoints

@router.get("/layer/parameters", response_model=LayerParametersResponse)
async def get_layer_parameters(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> LayerParametersResponse:
    """
    Get the current parameters of the layer.
    
    Returns the current layer parameters including filling drum ID, speed,
    powder saving settings, and X-axis offset.
    """
    try:
        logger.info("Getting layer parameters")
        result = recoater_client.get_layer_parameters()
        
        return LayerParametersResponse(**result)
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting layer parameters: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting layer parameters: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting layer parameters: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.put("/layer/parameters")
async def set_layer_parameters(
    parameters: LayerParametersRequest,
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the parameters of the current layer.
    
    Defines the parameters for the current layer including which drum contains
    the filling material, patterning speed, and powder saving settings.
    """
    try:
        logger.info(f"Setting layer parameters: {parameters.model_dump()}")
        
        result = recoater_client.set_layer_parameters(
            filling_id=parameters.filling_id,
            speed=parameters.speed,
            powder_saving=parameters.powder_saving,
            x_offset=parameters.x_offset
        )
        
        return {
            "success": True,
            "message": "Layer parameters set successfully",
            "parameters": parameters.model_dump()
        }
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting layer parameters: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting layer parameters: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error setting layer parameters: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/layer/preview")
async def get_layer_preview(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> Response:
    """
    Get layer preview as PNG image.
    
    Returns a PNG image preview of the layer showing the powder allocation.
    """
    try:
        logger.info("Getting layer preview")
        image_data = recoater_client.get_layer_preview()
        
        return Response(
            content=image_data,
            media_type="image/png",
            headers={"Content-Disposition": "inline; filename=layer_preview.png"}
        )
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting layer preview: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting layer preview: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting layer preview: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.post("/job", response_model=PrintJobResponse)
async def start_print_job(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> PrintJobResponse:
    """
    Create a printing job if the server is ready to start.
    
    Creates a printing job and starts the printing procedure. The recoater
    will wait for the synchronization signal.
    """
    try:
        logger.info("Starting print job")
        result = recoater_client.start_print_job()
        
        return PrintJobResponse(
            success=True,
            status="started",
            job_id=result.get("job_id")
        )
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error starting print job: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error starting print job: {e}")
        raise HTTPException(status_code=409, detail=f"Cannot start print job: {e}")
    except Exception as e:
        logger.error(f"Unexpected error starting print job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.delete("/job", response_model=PrintJobResponse)
async def cancel_print_job(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> PrintJobResponse:
    """
    Cancel and remove the current printing job.
    
    Cancels and removes the current printing job if one is running.
    """
    try:
        logger.info("Cancelling print job")
        result = recoater_client.cancel_print_job()
        
        return PrintJobResponse(
            success=True,
            status="cancelled"
        )
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error cancelling print job: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error cancelling print job: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error cancelling print job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/job/status", response_model=PrintJobStatusResponse)
async def get_print_job_status(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> PrintJobStatusResponse:
    """
    Get the current print job status.

    Returns the current state of the print job including whether it's printing,
    has errors, or is ready for new jobs.
    """
    try:
        logger.info("Getting print job status")
        result = recoater_client.get_print_job_status()

        return PrintJobStatusResponse(**result)

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting print job status: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting print job status: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting print job status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


# File Management Endpoints

@router.post("/drums/{drum_id}/geometry", response_model=FileUploadResponse)
async def upload_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    file: UploadFile = File(...),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> FileUploadResponse:
    """
    Upload geometry file (PNG or CLI) to a specific drum.

    Uploads a geometry file to the specified drum. The file can be either
    a PNG image or a CLI file containing layer geometry data.
    """
    try:
        logger.info(f"Uploading geometry file to drum {drum_id}: {file.filename}")

        # Read file data
        file_data = await file.read()

        # Upload to recoater
        result = recoater_client.upload_drum_geometry(
            drum_id=drum_id,
            file_data=file_data,
            content_type=file.content_type or "application/octet-stream"
        )

        return FileUploadResponse(
            success=True,
            message=f"File {file.filename} uploaded successfully to drum {drum_id}",
            drum_id=drum_id,
            file_size=len(file_data),
            content_type=file.content_type or "application/octet-stream"
        )

    except RecoaterConnectionError as e:
        logger.error(f"Connection error uploading file to drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error uploading file to drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error uploading file to drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/drums/{drum_id}/geometry")
async def download_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> Response:
    """
    Download geometry file from a specific drum as PNG image.

    Downloads the geometry file from the specified drum and returns it
    as a PNG image.
    """
    try:
        logger.info(f"Downloading geometry file from drum {drum_id}")

        # Download from recoater
        image_data = recoater_client.download_drum_geometry(drum_id)

        return Response(
            content=image_data,
            media_type="image/png",
            headers={"Content-Disposition": f"attachment; filename=drum_{drum_id}_geometry.png"}
        )

    except RecoaterConnectionError as e:
        logger.error(f"Connection error downloading file from drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error downloading file from drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error downloading file from drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.delete("/drums/{drum_id}/geometry", response_model=FileDeleteResponse)
async def delete_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> FileDeleteResponse:
    """
    Delete geometry file from a specific drum.

    Removes the geometry file from the specified drum.
    """
    try:
        logger.info(f"Deleting geometry file from drum {drum_id}")

        # Delete from recoater
        result = recoater_client.delete_drum_geometry(drum_id)

        return FileDeleteResponse(
            success=True,
            message=f"Geometry file deleted successfully from drum {drum_id}",
            drum_id=drum_id
        )

    except RecoaterConnectionError as e:
        logger.error(f"Connection error deleting file from drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error deleting file from drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error deleting file from drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")
