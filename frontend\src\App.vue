<template>
  <div id="app" class="app">
    <!-- Header with status indicator -->
    <header class="app-header">
      <div class="header-content">
        <h1 class="app-title">Recoater HMI</h1>
        <StatusIndicator />
      </div>
    </header>

    <!-- Main content area -->
    <main class="app-main">
      <nav class="app-nav">
        <ul class="nav-list">
          <li class="nav-item">
            <router-link to="/" class="nav-link" active-class="nav-link--active">
              Status
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/axis" class="nav-link" active-class="nav-link--active">
              Axis
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/recoater" class="nav-link" active-class="nav-link--active">
              Recoater
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/print" class="nav-link" active-class="nav-link--active">
              Print
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/config" class="nav-link" active-class="nav-link--active">
              Configuration
            </router-link>
          </li>
        </ul>
      </nav>

      <div class="app-content">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script>
import StatusIndicator from './components/StatusIndicator.vue'

export default {
  name: 'App',
  components: {
    StatusIndicator
  }
}
</script>

<style scoped>
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  background-color: #2c3e50;
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.app-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.app-main {
  flex: 1;
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.app-nav {
  width: 200px;
  background-color: #34495e;
  padding: 1rem 0;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: 0.5rem;
}

.nav-link {
  display: block;
  padding: 0.75rem 1.5rem;
  color: #ecf0f1;
  text-decoration: none;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: #3498db;
}

.nav-link--active {
  background-color: #3498db;
  font-weight: 600;
}

.app-content {
  flex: 1;
  padding: 2rem;
  background-color: #f8f9fa;
}
</style>
