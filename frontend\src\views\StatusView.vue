<template>
  <div class="status-view">
    <h2 class="view-title">System Status</h2>
    
    <div class="status-cards">
      <!-- Connection Status Card -->
      <div class="status-card">
        <h3 class="card-title">Connection Status</h3>
        <div class="card-content">
          <div class="status-item">
            <span class="status-label">Backend:</span>
            <span class="status-value" :class="backendStatusClass">
              {{ backendStatus }}
            </span>
          </div>
          <div class="status-item">
            <span class="status-label">Recoater:</span>
            <span class="status-value" :class="recoaterStatusClass">
              {{ recoaterStatus }}
            </span>
          </div>
          <div class="status-item" v-if="statusStore.lastUpdate">
            <span class="status-label">Last Update:</span>
            <span class="status-value">
              {{ formatTime(statusStore.lastUpdate) }}
            </span>
          </div>
        </div>
      </div>

      <!-- System Information Card -->
      <div class="status-card">
        <h3 class="card-title">System Information</h3>
        <div class="card-content">
          <div v-if="statusStore.recoaterStatus" class="status-item">
            <span class="status-label">State:</span>
            <span class="status-value">
              {{ statusStore.recoaterStatus.state || 'Unknown' }}
            </span>
          </div>
          <div v-if="!statusStore.recoaterStatus" class="status-item">
            <span class="status-value status-value--muted">
              No recoater data available
            </span>
          </div>
        </div>
      </div>

      <!-- Error Information Card -->
      <div class="status-card" v-if="statusStore.lastError">
        <h3 class="card-title">Error Information</h3>
        <div class="card-content">
          <div class="status-item">
            <span class="status-value status-value--error">
              {{ statusStore.lastError }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Manual Refresh Button -->
    <div class="actions">
      <button 
        class="btn btn--primary" 
        @click="refreshStatus"
        :disabled="isRefreshing"
      >
        {{ isRefreshing ? 'Refreshing...' : 'Refresh Status' }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStatusStore } from '../stores/status'

export default {
  name: 'StatusView',
  setup() {
    const statusStore = useStatusStore()
    const isRefreshing = ref(false)
    
    const backendStatus = computed(() => {
      return statusStore.isConnected ? 'Connected' : 'Disconnected'
    })
    
    const backendStatusClass = computed(() => {
      return statusStore.isConnected ? 'status-value--success' : 'status-value--error'
    })
    
    const recoaterStatus = computed(() => {
      if (!statusStore.isConnected) return 'Disconnected'
      if (statusStore.lastError) return 'Error'
      return statusStore.recoaterStatus ? 'Connected' : 'Unknown'
    })
    
    const recoaterStatusClass = computed(() => {
      if (!statusStore.isConnected || statusStore.lastError) {
        return 'status-value--error'
      }
      return statusStore.recoaterStatus ? 'status-value--success' : 'status-value--warning'
    })
    
    function formatTime(date) {
      return new Date(date).toLocaleTimeString()
    }
    
    async function refreshStatus() {
      isRefreshing.value = true
      try {
        await statusStore.fetchStatus()
      } catch (error) {
        console.error('Failed to refresh status:', error)
      } finally {
        isRefreshing.value = false
      }
    }
    
    onMounted(() => {
      // Fetch initial status
      refreshStatus()
    })
    
    return {
      statusStore,
      isRefreshing,
      backendStatus,
      backendStatusClass,
      recoaterStatus,
      recoaterStatusClass,
      formatTime,
      refreshStatus
    }
  }
}
</script>

<style scoped>
.status-view {
  max-width: 800px;
}

.view-title {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.status-cards {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.status-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.card-title {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-weight: 500;
  color: #5a6c7d;
}

.status-value {
  font-weight: 600;
}

.status-value--success {
  color: #27ae60;
}

.status-value--error {
  color: #e74c3c;
}

.status-value--warning {
  color: #f39c12;
}

.status-value--muted {
  color: #95a5a6;
  font-style: italic;
}

.actions {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn--primary {
  background-color: #3498db;
  color: white;
}

.btn--primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
