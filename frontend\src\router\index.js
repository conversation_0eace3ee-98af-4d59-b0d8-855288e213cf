/**
 * Vue Router Configuration
 * ========================
 * 
 * Defines the routing configuration for the Recoater HMI application.
 * Maps URLs to Vue components for navigation between different views.
 */

import { createRouter, createWebHistory } from 'vue-router'
import StatusView from '../views/StatusView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'status',
      component: StatusView
    },
    {
      path: '/axis',
      name: 'axis',
      component: () => import('../views/AxisView.vue')
    },
    {
      path: '/recoater',
      name: 'recoater',
      component: () => import('../views/RecoaterView.vue')
    },
    {
      path: '/print',
      name: 'print',
      component: () => import('../views/PrintView.vue')
    },
    {
      path: '/config',
      name: 'config',
      component: () => import('../views/ConfigurationView.vue')
    }
  ]
})

export default router
